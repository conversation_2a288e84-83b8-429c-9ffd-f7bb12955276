#!/usr/bin/env python3
"""
AWS VPN Network Analyzer
Advanced diagnostic tool for AWS VPN connectivity issues
"""

import boto3
import json
import socket
import subprocess
import sys
from datetime import datetime
from typing import Dict, List, Tuple
import argparse

class VPNNetworkAnalyzer:
    def __init__(self, region='me-south-1'):
        self.region = region
        self.ec2 = boto3.client('ec2', region_name=region)
        
        # Your VPN configuration
        self.vpn_connections = {
            'working': 'vpn-0e21045ac04035baf',
            'problematic': 'vpn-069f5b554655628d8'
        }
        
        # Test endpoints
        self.working_endpoints = {
            '10.173.52.30': [443],
            '10.173.43.152': [7002, 7004],
            '10.173.121.141': [7002, 7004],
            '10.173.121.142': [443]
        }
        
        self.failing_endpoints = {
            '172.30.52.75': [443],
            '172.30.52.13': [443]
        }

    def get_vpn_details(self, vpn_id: str) -> Dict:
        """Get detailed VPN connection information"""
        try:
            response = self.ec2.describe_vpn_connections(VpnConnectionIds=[vpn_id])
            return response['VpnConnections'][0]
        except Exception as e:
            print(f"Error getting VPN details for {vpn_id}: {e}")
            return {}

    def analyze_vpn_status(self) -> Dict:
        """Analyze the status of both VPN connections"""
        print("🔍 Analyzing VPN Connection Status...")
        
        analysis = {}
        for name, vpn_id in self.vpn_connections.items():
            vpn_details = self.get_vpn_details(vpn_id)
            if not vpn_details:
                continue
                
            analysis[name] = {
                'vpn_id': vpn_id,
                'state': vpn_details.get('State'),
                'type': vpn_details.get('Type'),
                'vpc_id': vpn_details.get('VpcId'),
                'customer_gateway_id': vpn_details.get('CustomerGatewayId'),
                'vpn_gateway_id': vpn_details.get('VpnGatewayId'),
                'tunnels': []
            }
            
            # Analyze tunnel status
            for tunnel in vpn_details.get('VgwTelemetry', []):
                tunnel_info = {
                    'outside_ip': tunnel.get('OutsideIpAddress'),
                    'status': tunnel.get('Status'),
                    'last_status_change': tunnel.get('LastStatusChange'),
                    'status_message': tunnel.get('StatusMessage', ''),
                    'accepted_route_count': tunnel.get('AcceptedRouteCount', 0)
                }
                analysis[name]['tunnels'].append(tunnel_info)
            
            # Get routes
            analysis[name]['routes'] = vpn_details.get('Routes', [])
            
        return analysis

    def check_route_tables(self, vpc_id: str) -> List[Dict]:
        """Check route tables for the VPC"""
        print("🛣️  Analyzing Route Tables...")
        
        try:
            response = self.ec2.describe_route_tables(
                Filters=[{'Name': 'vpc-id', 'Values': [vpc_id]}]
            )
            
            route_analysis = []
            for rt in response['RouteTables']:
                rt_info = {
                    'route_table_id': rt['RouteTableId'],
                    'is_main': any(assoc.get('Main', False) for assoc in rt.get('Associations', [])),
                    'routes': [],
                    'associated_subnets': []
                }
                
                # Analyze routes
                for route in rt.get('Routes', []):
                    route_info = {
                        'destination': route.get('DestinationCidrBlock', route.get('DestinationPrefixListId', 'N/A')),
                        'target': route.get('GatewayId', route.get('VpcPeeringConnectionId', 
                                          route.get('NetworkInterfaceId', route.get('InstanceId', 'local')))),
                        'state': route.get('State', 'N/A')
                    }
                    rt_info['routes'].append(route_info)
                
                # Get associated subnets
                for assoc in rt.get('Associations', []):
                    if 'SubnetId' in assoc:
                        rt_info['associated_subnets'].append(assoc['SubnetId'])
                
                route_analysis.append(rt_info)
            
            return route_analysis
            
        except Exception as e:
            print(f"Error analyzing route tables: {e}")
            return []

    def test_connectivity(self, endpoints: Dict[str, List[int]], test_name: str) -> Dict:
        """Test connectivity to endpoints"""
        print(f"🔌 Testing {test_name} Connectivity...")
        
        results = {}
        for ip, ports in endpoints.items():
            results[ip] = {}
            for port in ports:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    result = sock.connect_ex((ip, port))
                    sock.close()
                    
                    results[ip][port] = {
                        'success': result == 0,
                        'error_code': result if result != 0 else None
                    }
                except Exception as e:
                    results[ip][port] = {
                        'success': False,
                        'error': str(e)
                    }
        
        return results

    def check_security_groups(self, vpc_id: str) -> List[Dict]:
        """Analyze security groups in the VPC"""
        print("🔒 Analyzing Security Groups...")
        
        try:
            response = self.ec2.describe_security_groups(
                Filters=[{'Name': 'vpc-id', 'Values': [vpc_id]}]
            )
            
            sg_analysis = []
            for sg in response['SecurityGroups']:
                sg_info = {
                    'group_id': sg['GroupId'],
                    'group_name': sg['GroupName'],
                    'description': sg['Description'],
                    'inbound_rules': [],
                    'outbound_rules': []
                }
                
                # Analyze inbound rules
                for rule in sg.get('IpPermissions', []):
                    rule_info = {
                        'protocol': rule.get('IpProtocol', 'N/A'),
                        'from_port': rule.get('FromPort'),
                        'to_port': rule.get('ToPort'),
                        'sources': []
                    }
                    
                    for ip_range in rule.get('IpRanges', []):
                        rule_info['sources'].append(ip_range.get('CidrIp', 'N/A'))
                    
                    sg_info['inbound_rules'].append(rule_info)
                
                sg_analysis.append(sg_info)
            
            return sg_analysis
            
        except Exception as e:
            print(f"Error analyzing security groups: {e}")
            return []

    def generate_report(self) -> Dict:
        """Generate comprehensive diagnostic report"""
        print("📊 Generating Comprehensive Report...")
        
        # Get VPC ID from working VPN
        working_vpn = self.get_vpn_details(self.vpn_connections['working'])
        vpc_id = working_vpn.get('VpcId') if working_vpn else None
        
        if not vpc_id:
            print("❌ Could not determine VPC ID")
            return {}
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'region': self.region,
            'vpc_id': vpc_id,
            'vpn_analysis': self.analyze_vpn_status(),
            'route_tables': self.check_route_tables(vpc_id),
            'security_groups': self.check_security_groups(vpc_id),
            'connectivity_tests': {
                'working_endpoints': self.test_connectivity(self.working_endpoints, "Working Endpoints"),
                'failing_endpoints': self.test_connectivity(self.failing_endpoints, "Failing Endpoints")
            }
        }
        
        return report

    def print_summary(self, report: Dict):
        """Print a human-readable summary of the report"""
        print("\n" + "="*60)
        print("🎯 DIAGNOSTIC SUMMARY")
        print("="*60)
        
        # VPN Status Summary
        print("\n📡 VPN Connection Status:")
        for name, vpn_data in report['vpn_analysis'].items():
            status_emoji = "✅" if vpn_data['state'] == 'available' else "❌"
            print(f"  {status_emoji} {name.title()} VPN ({vpn_data['vpn_id']}): {vpn_data['state']}")
            
            for i, tunnel in enumerate(vpn_data['tunnels'], 1):
                tunnel_emoji = "🟢" if tunnel['status'] == 'UP' else "🔴"
                print(f"    {tunnel_emoji} Tunnel {i}: {tunnel['status']} ({tunnel['outside_ip']})")
        
        # Connectivity Summary
        print("\n🔌 Connectivity Test Results:")
        
        working_tests = report['connectivity_tests']['working_endpoints']
        failing_tests = report['connectivity_tests']['failing_endpoints']
        
        print("  ✅ Working Endpoints:")
        for ip, ports in working_tests.items():
            for port, result in ports.items():
                status = "✅" if result['success'] else "❌"
                print(f"    {status} {ip}:{port}")
        
        print("  ❌ Failing Endpoints:")
        for ip, ports in failing_tests.items():
            for port, result in ports.items():
                status = "✅" if result['success'] else "❌"
                print(f"    {status} {ip}:{port}")
        
        # Route Table Issues
        print(f"\n🛣️  Route Tables Found: {len(report['route_tables'])}")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS:")
        self.print_recommendations(report)

    def print_recommendations(self, report: Dict):
        """Print specific recommendations based on the analysis"""
        recommendations = []
        
        # Check VPN tunnel status
        for name, vpn_data in report['vpn_analysis'].items():
            if name == 'problematic':
                down_tunnels = [t for t in vpn_data['tunnels'] if t['status'] != 'UP']
                if down_tunnels:
                    recommendations.append(f"🔧 VPN {vpn_data['vpn_id']} has {len(down_tunnels)} tunnel(s) down. Check customer gateway configuration.")
        
        # Check for missing routes
        route_destinations = []
        for rt in report['route_tables']:
            for route in rt['routes']:
                route_destinations.append(route['destination'])
        
        if '172.30.0.0/16' not in route_destinations and '172.30.52.0/24' not in route_destinations:
            recommendations.append("🛣️  Missing routes for 172.30.x.x network. Add routes pointing to the correct VPN gateway.")
        
        # Check connectivity patterns
        failing_tests = report['connectivity_tests']['failing_endpoints']
        all_failing = all(
            not result['success'] 
            for ports in failing_tests.values() 
            for result in ports.values()
        )
        
        if all_failing:
            recommendations.append("🔌 All failing endpoints are unreachable. This suggests a routing or VPN tunnel issue rather than security group problems.")
        
        # Print recommendations
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
        
        if not recommendations:
            print("  ✅ No obvious issues detected. Manual investigation may be required.")

def main():
    parser = argparse.ArgumentParser(description='AWS VPN Network Analyzer')
    parser.add_argument('--region', default='me-south-1', help='AWS region (default: me-south-1)')
    parser.add_argument('--output', help='Output file for detailed JSON report')
    
    args = parser.parse_args()
    
    try:
        analyzer = VPNNetworkAnalyzer(region=args.region)
        report = analyzer.generate_report()
        
        if report:
            analyzer.print_summary(report)
            
            if args.output:
                with open(args.output, 'w') as f:
                    json.dump(report, f, indent=2, default=str)
                print(f"\n📄 Detailed report saved to: {args.output}")
        else:
            print("❌ Failed to generate report")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error running analysis: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
