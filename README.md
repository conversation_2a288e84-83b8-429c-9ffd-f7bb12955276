# AWS VPN Network Diagnostics Toolkit

This toolkit helps diagnose VPN connectivity issues in AWS, specifically designed for your Bahrain region setup with two VPN connections.

## Your Current Setup

### Working VPN Connection (vpn-0e21045ac04035baf)
- ✅ ************ (Production Active) Port 443
- ✅ ************* (UAT) Port 7002/7004  
- ✅ ************** (UAT) Port 7002/7004
- ✅ **************

### Problematic VPN Connection (vpn-069f5b554655628d8)
- ❌ ************ (Production Passive) Port 443
- ❌ ************ (PRE-PROD) Port 443

## Quick Start

### 1. Basic Bash Diagnostics
```bash
chmod +x aws-vpn-diagnostics.sh
./aws-vpn-diagnostics.sh
```

### 2. Advanced Python Analysis
```bash
# Install dependencies
pip3 install boto3

# Run analysis
python3 vpn_network_analyzer.py

# Save detailed report
python3 vpn_network_analyzer.py --output vpn_report.json
```

## Common Issues and Solutions

### 1. VPN Tunnel Status Issues
```bash
# Check tunnel status
aws ec2 describe-vpn-connections --vpn-connection-ids vpn-069f5b554655628d8 --region me-south-1

# Look for tunnel status in output
# Both tunnels should show "Status": "UP"
```

### 2. Route Table Problems
```bash
# List route tables for your VPC
aws ec2 describe-route-tables --filters "Name=vpc-id,Values=YOUR_VPC_ID" --region me-south-1

# Add missing route (replace with actual IDs)
aws ec2 create-route \
  --route-table-id rtb-xxxxxxxxx \
  --destination-cidr-block **********/16 \
  --vpn-gateway-id vgw-xxxxxxxxx \
  --region me-south-1
```

### 3. Security Group Issues
```bash
# Check security groups
aws ec2 describe-security-groups --filters "Name=vpc-id,Values=YOUR_VPC_ID" --region me-south-1

# Add rule to allow HTTPS traffic (replace with actual group ID)
aws ec2 authorize-security-group-ingress \
  --group-id sg-xxxxxxxxx \
  --protocol tcp \
  --port 443 \
  --cidr **********/16 \
  --region me-south-1
```

## Manual Testing Commands

### Test Connectivity
```bash
# Test from EC2 instance in your VPC
telnet ************ 443
telnet ************ 443

# Using netcat
nc -zv ************ 443
nc -zv ************ 443

# Using curl
curl -v --connect-timeout 10 https://************
curl -v --connect-timeout 10 https://************
```

### Check Routes from EC2 Instance
```bash
# Check routing table
ip route show

# Trace route
traceroute ************
traceroute ************

# Check if packets are being sent
sudo tcpdump -i any host ************
```

## Troubleshooting Checklist

### ✅ VPN Connection Level
- [ ] Both VPN connections show "available" state
- [ ] All tunnels show "UP" status
- [ ] Customer gateway configuration is correct
- [ ] BGP sessions are established (if using BGP)

### ✅ Routing Level
- [ ] Route tables include routes for **********/16 network
- [ ] Routes point to the correct VPN gateway
- [ ] Subnet associations are correct
- [ ] No conflicting routes exist

### ✅ Security Level
- [ ] Security groups allow traffic on required ports
- [ ] NACLs (Network ACLs) allow the traffic
- [ ] Customer-side firewall allows the traffic

### ✅ Network Level
- [ ] No IP address conflicts
- [ ] DNS resolution works (if applicable)
- [ ] MTU size is appropriate
- [ ] No packet loss in tunnels

## Advanced Diagnostics

### Check VPN Logs
```bash
# Get VPN connection configuration
aws ec2 describe-vpn-connections \
  --vpn-connection-ids vpn-069f5b554655628d8 \
  --region me-south-1 \
  --query 'VpnConnections[0].CustomerGatewayConfiguration' \
  --output text
```

### Monitor VPN Metrics
```bash
# Get CloudWatch metrics for VPN
aws cloudwatch get-metric-statistics \
  --namespace AWS/VPN \
  --metric-name TunnelState \
  --dimensions Name=VpnId,Value=vpn-069f5b554655628d8 \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-02T00:00:00Z \
  --period 3600 \
  --statistics Average \
  --region me-south-1
```

## Expected Output Analysis

### Healthy VPN Connection
```json
{
  "State": "available",
  "VgwTelemetry": [
    {
      "OutsideIpAddress": "x.x.x.x",
      "Status": "UP",
      "LastStatusChange": "2024-01-01T12:00:00.000Z",
      "AcceptedRouteCount": 1
    }
  ]
}
```

### Route Table Entry
```json
{
  "DestinationCidrBlock": "**********/16",
  "GatewayId": "vgw-xxxxxxxxx",
  "State": "active"
}
```

## Next Steps

1. **Run the diagnostic tools** to identify the specific issue
2. **Check the tunnel status** of vpn-069f5b554655628d8
3. **Verify routing** for the 172.30.x.x networks
4. **Test from an EC2 instance** in your VPC for more detailed network diagnostics
5. **Check customer gateway logs** if accessible

## Support Information

- **Region**: me-south-1 (Bahrain)
- **Working VPN**: vpn-0e21045ac04035baf
- **Problematic VPN**: vpn-069f5b554655628d8
- **Target Networks**: ************/32, ************/32

For additional help, run the diagnostic tools and share the output for more specific guidance.
