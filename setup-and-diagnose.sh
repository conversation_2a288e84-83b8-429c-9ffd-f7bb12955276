#!/bin/bash

# Setup and Diagnose VPN Issues
# This script will set up the environment and run diagnostics

set -e

echo "🚀 Setting up AWS VPN Diagnostics Environment..."

# Activate virtual environment
source venv/bin/activate

# Check if AWS credentials are configured
echo "📋 Checking AWS configuration..."
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS credentials not configured."
    echo ""
    echo "Please run one of the following:"
    echo "1. aws configure (and enter your credentials)"
    echo "2. Export environment variables:"
    echo "   export AWS_ACCESS_KEY_ID=your_access_key"
    echo "   export AWS_SECRET_ACCESS_KEY=your_secret_key"
    echo "   export AWS_DEFAULT_REGION=me-south-1"
    echo ""
    echo "Then run this script again."
    exit 1
fi

echo "✅ AWS credentials configured successfully"
aws sts get-caller-identity

# Make scripts executable
chmod +x aws-vpn-diagnostics.sh test-connectivity.sh

echo ""
echo "🔍 Running VPN Diagnostics..."
echo "================================"

# Run the bash diagnostics
echo "📊 Running comprehensive bash diagnostics..."
./aws-vpn-diagnostics.sh

echo ""
echo "🐍 Running Python analysis..."
python3 vpn_network_analyzer.py --output vpn_detailed_report.json

echo ""
echo "📄 Detailed JSON report saved to: vpn_detailed_report.json"

echo ""
echo "🎯 NEXT STEPS:"
echo "1. Review the diagnostic output above"
echo "2. If you have access to an EC2 instance in your VPC, run: ./test-connectivity.sh"
echo "3. Based on the findings, we can implement the specific fixes needed"

echo ""
echo "✅ Diagnostics complete! Please review the output and let me know what issues were found."
