#!/bin/bash

# AWS VPN Network Diagnostics Script
# This script helps diagnose VPN connectivity issues in AWS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
VPN1_ID="vpn-0e21045ac04035baf"  # Working VPN
VPN2_ID="vpn-069f5b554655628d8"  # Problematic VPN
VPC_ID=""  # Will be detected automatically
REGION="me-south-1"  # Bahrain region

# Test endpoints
declare -A WORKING_ENDPOINTS=(
    ["************"]="443"
    ["*************"]="7002,7004"
    ["**************"]="7002,7004"
    ["**************"]="443"
)

declare -A FAILING_ENDPOINTS=(
    ["************"]="443"
    ["************"]="443"
)

echo -e "${BLUE}=== AWS VPN Network Diagnostics ===${NC}"
echo "Region: $REGION"
echo "VPN 1 (Working): $VPN1_ID"
echo "VPN 2 (Issues): $VPN2_ID"
echo ""

# Function to check AWS CLI
check_aws_cli() {
    echo -e "${YELLOW}Checking AWS CLI...${NC}"
    if ! command -v aws &> /dev/null; then
        echo -e "${RED}AWS CLI not found. Please install it first.${NC}"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        echo -e "${RED}AWS credentials not configured. Please run 'aws configure'.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}AWS CLI configured successfully${NC}"
    aws sts get-caller-identity --query 'Account' --output text
    echo ""
}

# Function to get VPC ID
get_vpc_id() {
    echo -e "${YELLOW}Detecting VPC ID...${NC}"
    VPC_ID=$(aws ec2 describe-vpn-connections --vpn-connection-ids $VPN1_ID --region $REGION --query 'VpnConnections[0].VpcId' --output text)
    if [ "$VPC_ID" = "None" ] || [ -z "$VPC_ID" ]; then
        echo -e "${RED}Could not detect VPC ID. Please set it manually.${NC}"
        exit 1
    fi
    echo -e "${GREEN}VPC ID: $VPC_ID${NC}"
    echo ""
}

# Function to check VPN connection status
check_vpn_status() {
    echo -e "${YELLOW}=== VPN Connection Status ===${NC}"
    
    for vpn_id in $VPN1_ID $VPN2_ID; do
        echo -e "${BLUE}Checking VPN: $vpn_id${NC}"
        
        # Get VPN connection details
        vpn_info=$(aws ec2 describe-vpn-connections --vpn-connection-ids $vpn_id --region $REGION)
        
        state=$(echo $vpn_info | jq -r '.VpnConnections[0].State')
        type=$(echo $vpn_info | jq -r '.VpnConnections[0].Type')
        
        echo "  State: $state"
        echo "  Type: $type"
        
        # Check tunnel status
        echo "  Tunnel Status:"
        echo $vpn_info | jq -r '.VpnConnections[0].VgwTelemetry[] | "    Tunnel \(.OutsideIpAddress): \(.Status) (Last Status Change: \(.LastStatusChange))"'
        
        # Get customer gateway
        cgw_id=$(echo $vpn_info | jq -r '.VpnConnections[0].CustomerGatewayId')
        echo "  Customer Gateway: $cgw_id"
        
        echo ""
    done
}

# Function to check route tables
check_route_tables() {
    echo -e "${YELLOW}=== Route Table Analysis ===${NC}"
    
    # Get all route tables for the VPC
    route_tables=$(aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" --region $REGION)
    
    echo $route_tables | jq -r '.RouteTables[] | 
        "Route Table: \(.RouteTableId) (\(.Associations[0].Main // false | if . then "Main" else "Custom" end))" as $header |
        $header,
        (.Routes[] | "  \(.DestinationCidrBlock // .DestinationPrefixListId // "N/A") -> \(.GatewayId // .VpcPeeringConnectionId // .NetworkInterfaceId // .InstanceId // "local")")' 2>/dev/null || echo "Error parsing route tables"
    
    echo ""
}

# Function to check security groups
check_security_groups() {
    echo -e "${YELLOW}=== Security Group Analysis ===${NC}"
    
    # Get security groups that might affect VPN traffic
    security_groups=$(aws ec2 describe-security-groups --filters "Name=vpc-id,Values=$VPC_ID" --region $REGION)
    
    echo "Security Groups in VPC $VPC_ID:"
    echo $security_groups | jq -r '.SecurityGroups[] | "  \(.GroupId): \(.GroupName) - \(.Description)"'
    
    echo ""
}

# Function to test connectivity
test_connectivity() {
    echo -e "${YELLOW}=== Connectivity Tests ===${NC}"
    
    echo -e "${GREEN}Testing Working Endpoints:${NC}"
    for ip in "${!WORKING_ENDPOINTS[@]}"; do
        ports="${WORKING_ENDPOINTS[$ip]}"
        IFS=',' read -ra PORT_ARRAY <<< "$ports"
        for port in "${PORT_ARRAY[@]}"; do
            echo -n "  Testing $ip:$port ... "
            if timeout 5 bash -c "</dev/tcp/$ip/$port" 2>/dev/null; then
                echo -e "${GREEN}SUCCESS${NC}"
            else
                echo -e "${RED}FAILED${NC}"
            fi
        done
    done
    
    echo ""
    echo -e "${RED}Testing Failing Endpoints:${NC}"
    for ip in "${!FAILING_ENDPOINTS[@]}"; do
        ports="${FAILING_ENDPOINTS[$ip]}"
        IFS=',' read -ra PORT_ARRAY <<< "$ports"
        for port in "${PORT_ARRAY[@]}"; do
            echo -n "  Testing $ip:$port ... "
            if timeout 5 bash -c "</dev/tcp/$ip/$port" 2>/dev/null; then
                echo -e "${GREEN}SUCCESS${NC}"
            else
                echo -e "${RED}FAILED${NC}"
            fi
        done
    done
    
    echo ""
}

# Function to check VPN configuration
check_vpn_config() {
    echo -e "${YELLOW}=== VPN Configuration Details ===${NC}"
    
    for vpn_id in $VPN1_ID $VPN2_ID; do
        echo -e "${BLUE}VPN Configuration: $vpn_id${NC}"
        
        # Get detailed VPN configuration
        aws ec2 describe-vpn-connections --vpn-connection-ids $vpn_id --region $REGION | \
        jq -r '.VpnConnections[0] | 
            "  Customer Gateway Configuration:",
            "    BGP ASN: \(.CustomerGatewayConfiguration // "N/A")",
            "  Routes:",
            (.Routes[]? // [] | "    \(.DestinationCidrBlock) - \(.State)"),
            "  Options:",
            "    Static Routes Only: \(.Options.StaticRoutesOnly // "N/A")"'
        
        echo ""
    done
}

# Function to generate recommendations
generate_recommendations() {
    echo -e "${YELLOW}=== Troubleshooting Recommendations ===${NC}"
    
    cat << EOF
Based on your setup, here are potential issues and solutions:

1. ${BLUE}Route Table Issues:${NC}
   - Check if routes for 172.30.x.x networks are pointing to the correct VPN gateway
   - Verify that the route table associated with your subnets includes the failing networks
   - Command: aws ec2 create-route --route-table-id <rt-id> --destination-cidr-block 172.30.0.0/16 --vpn-gateway-id <vgw-id>

2. ${BLUE}VPN Tunnel Status:${NC}
   - Ensure both tunnels of VPN $VPN2_ID are UP
   - Check for BGP session issues if using dynamic routing
   - Verify customer gateway configuration

3. ${BLUE}Security Groups:${NC}
   - Verify security groups allow traffic on port 443 from your source
   - Check NACLs (Network Access Control Lists) for the subnets

4. ${BLUE}Customer Gateway Configuration:${NC}
   - Verify the customer gateway for $VPN2_ID is configured correctly
   - Check if there are any firewall rules blocking the 172.30.x.x networks

5. ${BLUE}Network Overlap:${NC}
   - Ensure there's no IP address overlap between the networks
   - Check if both VPN connections have conflicting routes

6. ${BLUE}BGP Configuration (if applicable):${NC}
   - Verify BGP announcements for the 172.30.x.x networks
   - Check BGP session status and route advertisements

EOF
}

# Main execution
main() {
    check_aws_cli
    get_vpc_id
    check_vpn_status
    check_route_tables
    check_security_groups
    check_vpn_config
    test_connectivity
    generate_recommendations
    
    echo -e "${GREEN}=== Diagnostic Complete ===${NC}"
    echo "Review the output above to identify potential issues with your VPN connectivity."
}

# Run main function
main
