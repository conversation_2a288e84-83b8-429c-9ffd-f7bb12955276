#!/bin/bash

# Simple connectivity test script for VPN endpoints
# Run this from an EC2 instance in your VPC

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== VPN Connectivity Test ===${NC}"
echo "Testing connectivity to VPN endpoints..."
echo ""

# Working endpoints (should work)
echo -e "${GREEN}Testing Working Endpoints:${NC}"
declare -A working_endpoints=(
    ["************"]="443"
    ["*************"]="7002 7004"
    ["**************"]="7002 7004"
    ["**************"]="443"
)

for ip in "${!working_endpoints[@]}"; do
    ports="${working_endpoints[$ip]}"
    for port in $ports; do
        echo -n "  Testing $ip:$port ... "
        if timeout 5 bash -c "echo >/dev/tcp/$ip/$port" 2>/dev/null; then
            echo -e "${GREEN}✓ SUCCESS${NC}"
        else
            echo -e "${RED}✗ FAILED${NC}"
        fi
    done
done

echo ""

# Failing endpoints (currently not working)
echo -e "${RED}Testing Problematic Endpoints:${NC}"
declare -A failing_endpoints=(
    ["************"]="443"
    ["************"]="443"
)

for ip in "${!failing_endpoints[@]}"; do
    ports="${failing_endpoints[$ip]}"
    for port in $ports; do
        echo -n "  Testing $ip:$port ... "
        if timeout 5 bash -c "echo >/dev/tcp/$ip/$port" 2>/dev/null; then
            echo -e "${GREEN}✓ SUCCESS${NC}"
        else
            echo -e "${RED}✗ FAILED${NC}"
        fi
    done
done

echo ""

# Additional network diagnostics
echo -e "${YELLOW}=== Network Diagnostics ===${NC}"

# Check routing
echo "Current routing table:"
ip route show | grep -E "(172\.30|10\.173)" || echo "No specific routes found for target networks"

echo ""

# Test with ping (if ICMP is allowed)
echo "Testing ICMP connectivity:"
for ip in "************" "************"; do
    echo -n "  Ping $ip ... "
    if ping -c 1 -W 3 $ip >/dev/null 2>&1; then
        echo -e "${GREEN}✓ SUCCESS${NC}"
    else
        echo -e "${RED}✗ FAILED${NC}"
    fi
done

echo ""

# Traceroute to problematic endpoints
echo "Traceroute to problematic endpoints:"
for ip in "************" "************"; do
    echo "  Traceroute to $ip:"
    timeout 10 traceroute -m 5 $ip 2>/dev/null | head -10 | sed 's/^/    /' || echo "    Traceroute failed or timed out"
    echo ""
done

# DNS resolution test (if applicable)
echo "DNS resolution test:"
for ip in "************" "************"; do
    echo -n "  Reverse DNS for $ip ... "
    if nslookup $ip >/dev/null 2>&1; then
        hostname=$(nslookup $ip | grep "name =" | cut -d' ' -f3)
        echo -e "${GREEN}$hostname${NC}"
    else
        echo -e "${YELLOW}No reverse DNS${NC}"
    fi
done

echo ""
echo -e "${BLUE}=== Test Complete ===${NC}"
echo "If the problematic endpoints are still failing, check:"
echo "1. VPN tunnel status"
echo "2. Route table configuration"
echo "3. Security group rules"
echo "4. Customer gateway configuration"
